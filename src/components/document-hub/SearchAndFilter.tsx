
import React, { useState } from "react";
import { FilterSidePanel } from "@/components/filters/filter-panel/FilterSidePanel";
import { FilterState } from "@/types/filter";
import { useIsMobile } from "@/hooks/use-mobile";
import { CreateDocumentDialog } from "./CreateDocumentDialog";
import { statusOptions } from "@/data/filterOptions";
import { SearchBar } from "./search-filter/SearchBar";
import { DesktopActions } from "./search-filter/DesktopActions";
import { MobileActions } from "./search-filter/MobileActions";
import { StatusFilter } from "./search-filter/StatusFilter";

interface SearchAndFilterProps {
  isFilterSidePanelOpen: boolean;
  toggleFilterSidePanel: () => void;
  appliedFilters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onSearch: (searchTerm: string) => void;
  searchTerm?: string;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  isFilterSidePanelOpen,
  toggleFilterSidePanel,
  appliedFilters,
  onApplyFilters,
  onSearch,
  searchTerm = "",
}) => {
  const isMobile = useIsMobile();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  
  const countActiveFilters = (): number => {
    return Object.entries(appliedFilters)
      .filter(([key]) => key !== "status")
      .reduce((count, [_, filterArray]) => count + filterArray.length, 0);
  };

  const handleCreateClick = () => {
    setIsCreateDialogOpen(true);
  };

  const handleDialogClose = (success?: boolean) => {
    setIsCreateDialogOpen(false);
    if (success) {
      console.log("Document created successfully");
    }
  };

  const handleStatusChange = (selectedStatuses: string[]) => {
    const updatedFilters = {
      ...appliedFilters,
      status: selectedStatuses
    };
    onApplyFilters(updatedFilters);
  };

  const resetAllFilters = () => {
    onApplyFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: []
    });
  };

  const toggleSearch = () => {
    setIsSearchExpanded(!isSearchExpanded);
  };

  const handleSearch = (term: string) => {
    onSearch(term);
    console.log("Search term in SearchAndFilter:", term);
  };

  return (
    <div className={`bg-white px-4 ${isMobile ? 'py-2' : 'py-4'} border-b border-gray-200`}>
      <div className="max-w-full flex items-center gap-4 justify-between">
        <div className={`${isMobile ? 'flex-grow' : 'w-96 max-w-md'}`}>
          <SearchBar 
            onSearch={handleSearch} 
            initialSearchTerm={searchTerm} 
            expanded={isSearchExpanded}
            onToggleExpand={toggleSearch}
          />
        </div>
        
        {(!isMobile || !isSearchExpanded) && (
          <div className="flex items-center gap-3">
            <StatusFilter
              appliedFilters={appliedFilters}
              handleStatusChange={handleStatusChange}
              statusOptions={statusOptions}
            />
            
            {!isMobile ? (
              <DesktopActions 
                toggleFilterSidePanel={toggleFilterSidePanel}
                handleCreateClick={handleCreateClick}
                appliedFilters={appliedFilters}
                handleStatusChange={handleStatusChange}
                statusOptions={statusOptions}
                countActiveFilters={countActiveFilters}
                resetAllFilters={resetAllFilters}
              />
            ) : (
              <MobileActions
                toggleFilterSidePanel={toggleFilterSidePanel}
                handleCreateClick={handleCreateClick}
                appliedFilters={appliedFilters}
                handleStatusChange={handleStatusChange}
                statusOptions={statusOptions}
                countActiveFilters={countActiveFilters}
                resetAllFilters={resetAllFilters}
              />
            )}
          </div>
        )}

        <FilterSidePanel
          isOpen={isFilterSidePanelOpen}
          onClose={toggleFilterSidePanel}
          filters={appliedFilters}
          onApplyFilters={onApplyFilters}
        />
        
        <CreateDocumentDialog 
          open={isCreateDialogOpen}
          onOpenChange={handleDialogClose}
        />
      </div>
    </div>
  );
};
