import React, {
  useState,
  useMemo,
  useEffect,
  useRef,
  useCallback,
} from "react";
import {
  Search,
  X,
  Filter,
  Plus,
  RotateCcw,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronsLeft,
  ChevronsRight,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Edit,
  Download,
  Eye,
  MoreVertical,
  MessageSquare,
} from "lucide-react";

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId?: string | null;
}

interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
  processes?: string[];
}

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

type SortField = keyof Document | null;
type SortDirection = "asc" | "desc" | null;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function cn(...inputs: any[]) {
  return inputs.filter(Boolean).join(" ");
}

// --- NEW: Data Normalization and Dynamic Filter Option Generation ---

function normalizeDocuments(rawDocs: any[]): Document[] {
  return rawDocs.map((doc) => ({
    id: doc.id,
    title: doc.title,
    version: doc.document_version?.version_number?.toString() ?? doc.version ?? "",
    department: doc.department?.name ?? doc.department ?? "",
    category: doc.category?.name ?? doc.category ?? "",
    processes: Array.isArray(doc.processes)
      ? doc.processes.map((p: any) => p.name).join(", ")
      : doc.processes ?? "",
    status: doc.status?.toLowerCase?.() ?? doc.status ?? "",
    assignee: Array.isArray(doc.assignees)
      ? doc.assignees.map((a: any) => a.full_name).join(", ")
      : doc.assignee ?? "",
    approver: Array.isArray(doc.approvers)
      ? doc.approvers.map((a: any) => a.full_name).join(", ")
      : doc.approver ?? "",
    date: doc.date,
    publishedDate: doc.publishedDate,
    reviewDate: doc.reviewDate,
    description: doc.description,
    parentFolderId: doc.parentFolderId ?? null,
  }));
}

function generateFilterOptions(
  documents: Document[],
  field: keyof Document
): FilterOption[] {
  const counts: Record<string, number> = {};
  documents.forEach((doc) => {
    let values: string[] = [];
    if (Array.isArray(doc[field])) {
      values = doc[field] as string[];
    } else if (typeof doc[field] === "string") {
      values = (doc[field] as string)
        .split(",")
        .map((v) => v.trim())
        .filter(Boolean);
    }
    values.forEach((value) => {
      if (!value) return;
      counts[value] = (counts[value] || 0) + 1;
    });
  });
  return Object.entries(counts).map(([id, count]) => ({
    id,
    label: id,
    count,
  }));
}

const MOBILE_BREAKPOINT = 768;

function useIsMobile() {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);

  useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };
    mql.addEventListener("change", onChange);
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    return () => mql.removeEventListener("change", onChange);
  }, []);

  return !!isMobile;
}

// ============================================================================
// MOCK DATA
// ============================================================================

// --- REPLACE: mockDocuments with dynamic normalization ---
const rawSampleDocuments: any[] = [
  // Example sample data (replace or extend as needed)
  {
    id: "e046e08f-97b7-49f9-8f4a-46024098a795",
    doc_id: "Approval flow testing - 01",
    title: "Approval flow testing - 01",
    department: { name: "Project Management" },
    category: { name: "test" },
    assignees: [
      { full_name: "Elon Musk" },
      { full_name: "Neil Night" },
    ],
    approvers: [{ full_name: "Elon Musk" }],
    processes: [{ name: "Quality Assurance" }],
    status: "Published",
    document_version: { version_number: 3.0 },
  },
  // ... you can add more sample documents here ...
];

const mockDocuments: Document[] = normalizeDocuments(rawSampleDocuments);

// ============================================================================
// HOOKS
// ============================================================================

interface UseDocumentFilterProps {
  documents: Document[];
  appliedFilters: FilterState;
  initialSearchTerm?: string;
}

const useDocumentFilter = ({
  documents,
  appliedFilters,
  initialSearchTerm = "",
}: UseDocumentFilterProps) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  useEffect(() => {
    setSearchTerm(initialSearchTerm);
  }, [initialSearchTerm]);

  const filteredDocuments = useMemo(() => {
    return documents.filter((doc) => {
      const searchMatch =
        searchTerm.length < 3 ||
        Object.values(doc).some(
          (value) =>
            value &&
            typeof value === "string" &&
            value.toLowerCase().includes(searchTerm.toLowerCase())
        );

      const statusMatch =
        appliedFilters.status.length === 0 ||
        appliedFilters.status.some(
          (s) => doc.status?.toLowerCase() === s.toLowerCase()
        );

      const categoryMatch =
        appliedFilters.categories.length === 0 ||
        appliedFilters.categories.some(
          (c) => doc.category?.toLowerCase() === c.toLowerCase()
        );

      const departmentMatch =
        appliedFilters.departments.length === 0 ||
        appliedFilters.departments.some(
          (d) => doc.department?.toLowerCase() === d.toLowerCase()
        );

      const assigneeMatch =
        appliedFilters.assignee.length === 0 ||
        appliedFilters.assignee.some(
          (a) => doc.assignee?.toLowerCase() === a.toLowerCase()
        );

      const processesMatch =
        !appliedFilters.processes ||
        appliedFilters.processes.length === 0 ||
        (doc.processes &&
          appliedFilters.processes.some((p) => {
            const docProcesses = doc.processes
              ?.split(",")
              .map((item) => item.trim().toLowerCase());
            return docProcesses?.includes(p.toLowerCase());
          }));

      return (
        searchMatch &&
        statusMatch &&
        categoryMatch &&
        departmentMatch &&
        assigneeMatch &&
        processesMatch
      );
    });
  }, [documents, appliedFilters, searchTerm]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  return {
    filteredDocuments,
    searchTerm,
    handleSearch,
  };
};

interface UseDocumentPaginationProps {
  totalCount: number;
  initialPageSize?: number;
  initialPage?: number;
}

const useDocumentPagination = ({
  totalCount,
  initialPageSize = 10,
  initialPage = 1,
}: UseDocumentPaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const totalPages = useMemo(
    () => Math.ceil(totalCount / pageSize),
    [totalCount, pageSize]
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const getPaginatedData = (data: any[]): any[] => {
    return data.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  };

  return {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData,
  };
};

// ============================================================================
// UI COMPONENTS
// ============================================================================

// Basic UI Components
const Button = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "outline" | "ghost";
    size?: "default" | "sm" | "lg" | "icon";
  }
>(({ className, variant = "default", size = "default", ...props }, ref) => {
  const baseClasses =
    "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background";

  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700",
    outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",
    ghost: "hover:bg-gray-100 text-gray-700",
  };

  const sizes = {
    default: "h-10 py-2 px-4",
    sm: "h-9 px-3 rounded-md",
    lg: "h-11 px-8 rounded-md",
    icon: "h-10 w-10",
  };

  return (
    <button
      className={cn(baseClasses, variants[variant], sizes[size], className)}
      ref={ref}
      {...props}
    />
  );
});

const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <div className="w-full overflow-auto">
    <table
      ref={ref}
      className={cn("w-full caption-bottom text-sm", className)}
      {...props}
    />
  </div>
));

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
));

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
));

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
      className
    )}
    {...props}
  />
));

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <th
    ref={ref}
    className={cn(
      "h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",
      className
    )}
    {...props}
  />
));

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={cn("p-4 align-middle [&:has([role=checkbox])]:pr-0", className)}
    {...props}
  />
));

// Select Components
const Select = ({ children, value, onValueChange, ...props }: any) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(".select-container")) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  const handleValueChange = (newValue: string) => {
    onValueChange?.(newValue);
    setIsOpen(false);
  };

  return (
    <div className="relative select-container" {...props}>
      {React.Children.map(children, (child) =>
        React.cloneElement(child, {
          isOpen,
          setIsOpen,
          selectedValue: value,
          onValueChange: handleValueChange,
        })
      )}
    </div>
  );
};

const SelectTrigger = ({
  children,
  className,
  isOpen,
  setIsOpen,
  selectedValue,
  ...props
}: any) => (
  <button
    className={cn(
      "flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50",
      className
    )}
    onClick={() => setIsOpen?.(!isOpen)}
    {...props}
  >
    {React.Children.map(children, (child) =>
      React.cloneElement(child, { selectedValue })
    )}
    <ChevronDown className="h-4 w-4 opacity-50" />
  </button>
);

const SelectValue = ({ placeholder, selectedValue }: any) => {
  // For status filter, map value to label
  const getDisplayValue = () => {
    if (!selectedValue) return placeholder;
    if (selectedValue === "all") return "All Statuses";

    // Find the label from statusOptions
    const statusOption = statusOptions.find((opt) => opt.id === selectedValue);
    if (statusOption) return statusOption.label;

    return selectedValue;
  };

  return <span>{getDisplayValue()}</span>;
};

const SelectContent = ({ children, isOpen, onValueChange, ...props }: any) => {
  if (!isOpen) return null;

  return (
    <div
      className="absolute top-full left-0 z-50 w-full min-w-[8rem] overflow-hidden rounded-md border bg-white p-1 shadow-lg border-gray-200"
      {...props}
    >
      {React.Children.map(children, (child) =>
        React.cloneElement(child, { onValueChange })
      )}
    </div>
  );
};

const SelectGroup = ({ children, onValueChange }: any) => (
  <div className="p-1">
    {React.Children.map(children, (child) =>
      React.cloneElement(child, { onValueChange })
    )}
  </div>
);

const SelectItem = ({ children, value, onValueChange }: any) => (
  <div
    className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-2 px-3 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100"
    onClick={() => onValueChange?.(value)}
  >
    {children}
  </div>
);

// Tooltip Components
const TooltipProvider = ({ children }: any) => <>{children}</>;
const Tooltip = ({ children }: any) => {
  const [isVisible, setIsVisible] = useState(false);
  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {React.Children.map(children, (child) =>
        React.cloneElement(child, { isVisible })
      )}
    </div>
  );
};

const TooltipTrigger = ({ children, asChild, ...props }: any) => {
  if (asChild) {
    return React.cloneElement(children, props);
  }
  return <div {...props}>{children}</div>;
};

const TooltipContent = ({ children, isVisible }: any) => {
  if (!isVisible) return null;
  return (
    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-black rounded shadow-lg whitespace-nowrap z-50">
      {children}
    </div>
  );
};

// DropdownMenu Components
const DropdownMenu = ({ children }: any) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(".dropdown-container")) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  return (
    <div className="relative dropdown-container">
      {React.Children.map(children, (child) =>
        React.cloneElement(child, { isOpen, setIsOpen })
      )}
    </div>
  );
};

const DropdownMenuTrigger = ({ children, isOpen, setIsOpen, asChild }: any) => {
  if (asChild) {
    return React.cloneElement(children, {
      onClick: () => setIsOpen?.(!isOpen),
    });
  }
  return <div onClick={() => setIsOpen?.(!isOpen)}>{children}</div>;
};

const DropdownMenuContent = ({
  children,
  isOpen,
  align = "end",
  className = "",
}: any) => {
  if (!isOpen) return null;

  const alignmentClasses = {
    start: "left-0",
    center: "left-1/2 transform -translate-x-1/2",
    end: "right-0",
  };

  return (
    <div
      className={`absolute top-full ${alignmentClasses[align]} z-50 min-w-[8rem] overflow-hidden rounded-md border bg-white p-1 shadow-lg mt-1 ${className}`}
    >
      {children}
    </div>
  );
};

const DropdownMenuItem = ({ children, onClick, className = "" }: any) => (
  <div
    className={`relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 ${className}`}
    onClick={onClick}
  >
    {children}
  </div>
);

// Document Actions Component
interface DocumentActionsProps {
  document: Document;
}

const DocumentActions: React.FC<DocumentActionsProps> = ({ document }) => {
  const handleAction = (action: string) => {
    console.log(`Action clicked: ${action} for document:`, document.id);

    if (action === "View") {
      console.log("Navigating to document details:", document.id);
      // In a real app, this would navigate to the document details page
      alert(`View Details: ${document.title}`);
    } else if (action === "Comments") {
      console.log("Navigating to document comments:", document.id);
      // In a real app, this would navigate to the document comments
      alert(`View Comments: ${document.title}`);
    } else if (action === "Edit") {
      console.log("Edit document:", document);
      alert(`Edit document: ${document.title}`);
    } else if (action === "Download") {
      console.log("Download document:", document);
      alert(`Download document: ${document.title}`);
    }
  };

  return (
    <div className="flex items-center justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-100"
          >
            <MoreVertical className="h-4 w-4 text-gray-600" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          <DropdownMenuItem
            onClick={() => handleAction("View")}
            className="flex items-center justify-start"
          >
            <Eye className="mr-2 h-4 w-4 text-blue-600" />
            <span className="whitespace-nowrap">View Details</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleAction("Comments")}
            className="flex items-center justify-start"
          >
            <MessageSquare className="mr-2 h-4 w-4 text-blue-600" />
            <span className="whitespace-nowrap">View Comments</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleAction("Edit")}
            className="flex items-center justify-start"
          >
            <Edit className="mr-2 h-4 w-4 text-blue-600" />
            <span className="whitespace-nowrap">Edit</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => handleAction("Download")}
            className="flex items-center justify-start"
          >
            <Download className="mr-2 h-4 w-4 text-blue-600" />
            <span className="whitespace-nowrap">Download</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// ============================================================================
// SEARCH AND FILTER COMPONENTS
// ============================================================================

interface SearchBarProps {
  onSearch: (searchTerm: string) => void;
  initialSearchTerm?: string;
  expanded?: boolean;
  onToggleExpand?: () => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  initialSearchTerm = "",
  expanded = false,
  className,
}) => {
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [isSearchExpanded, setIsSearchExpanded] = useState(
    expanded || !isMobile
  );
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.length >= 3 || value.length === 0) {
      onSearch(value);
    }
  };

  const clearSearch = () => {
    setSearchTerm("");
    onSearch("");
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  useEffect(() => {
    setIsSearchExpanded(expanded);
  }, [expanded]);

  useEffect(() => {
    if (isMobile && isSearchExpanded && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isMobile, isSearchExpanded]);

  return (
    <div className={cn("relative w-full", className)}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>

      <input
        ref={searchInputRef}
        type="text"
        value={searchTerm}
        onChange={handleSearchChange}
        className={cn(
          "block w-full pl-10 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-600 focus:border-teal-600 placeholder-gray-500",
          isMobile ? "py-3 text-base h-12" : "py-2 text-sm"
        )}
        placeholder="Search documents..."
      />

      {searchTerm.length > 0 && (
        <button
          onClick={clearSearch}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
        >
          <X className="h-5 w-5" />
        </button>
      )}
    </div>
  );
};

interface StatusFilterProps {
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: FilterOption[];
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  appliedFilters,
  handleStatusChange,
  statusOptions,
}) => {
  const isMobile = useIsMobile();

  const handleChange = (value: string) => {
    if (value === "all") {
      handleStatusChange([]);
    } else {
      handleStatusChange([value]);
    }
  };

  return (
    <div className="min-w-[150px]">
      <Select
        value={
          appliedFilters.status.length > 0 ? appliedFilters.status[0] : "all"
        }
        onValueChange={handleChange}
      >
        <SelectTrigger
          className={`border-gray-300 ${isMobile ? "h-8 text-xs" : "h-10"}`}
        >
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Statuses</SelectItem>
            {statusOptions.map((option) => (
              <SelectItem key={option.id} value={option.id}>
                {option.label} ({option.count})
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

interface SearchAndFilterProps {
  isFilterSidePanelOpen: boolean;
  toggleFilterSidePanel: () => void;
  appliedFilters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onSearch: (searchTerm: string) => void;
  searchTerm?: string;
  statusOptions: FilterOption[];
  categoriesOptions: FilterOption[];
  departmentsOptions: FilterOption[];
  assigneeOptions: FilterOption[];
  processesOptions: FilterOption[];
}

const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  toggleFilterSidePanel,
  appliedFilters,
  onApplyFilters,
  onSearch,
  searchTerm = "",
  statusOptions,
  categoriesOptions,
  departmentsOptions,
  assigneeOptions,
  processesOptions,
}) => {
  const isMobile = useIsMobile();
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);

  const countActiveFilters = (): number => {
    return Object.entries(appliedFilters)
      .filter(([key]) => key !== "status")
      .reduce((count, [_, filterArray]) => count + filterArray.length, 0);
  };

  const handleCreateClick = () => {
    console.log("Create document clicked");
  };

  const handleStatusChange = (selectedStatuses: string[]) => {
    const updatedFilters = {
      ...appliedFilters,
      status: selectedStatuses,
    };
    onApplyFilters(updatedFilters);
  };

  const resetAllFilters = () => {
    onApplyFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: [],
    });
  };

  const toggleSearch = () => {
    setIsSearchExpanded(!isSearchExpanded);
  };

  const handleSearch = (term: string) => {
    onSearch(term);
  };

  return (
    <div
      className={`bg-white px-4 ${
        isMobile ? "py-2" : "py-4"
      } border-b border-gray-200`}
    >
      <div className="max-w-full flex items-center gap-4 justify-between">
        <div className={`${isMobile ? "flex-grow" : "w-96 max-w-md"}`}>
          <SearchBar
            onSearch={handleSearch}
            initialSearchTerm={searchTerm}
            expanded={isSearchExpanded}
            onToggleExpand={toggleSearch}
          />
        </div>

        {(!isMobile || !isSearchExpanded) && (
          <div className="flex items-center gap-3">
            <StatusFilter
              appliedFilters={appliedFilters}
              handleStatusChange={handleStatusChange}
              statusOptions={statusOptions}
            />

            {!isMobile ? (
              <>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={toggleFilterSidePanel}
                        className="inline-flex items-center shrink-0 text-base h-10 border border-gray-300 bg-white px-4 py-2 gap-2"
                      >
                        <Filter size={16} className="text-gray-700" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Filter Documents</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={resetAllFilters}
                        className="h-10 w-10 border border-gray-300 bg-white"
                      >
                        <RotateCcw size={16} className="text-gray-700" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Reset Filters</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        className="inline-flex items-center shrink-0 px-4 py-2 text-base h-10 bg-teal-600 hover:bg-teal-700 gap-2"
                        onClick={handleCreateClick}
                      >
                        <Plus size={16} />
                        Create
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Create Document</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-10 w-10"
                          >
                            <MoreVertical size={16} className="text-gray-600" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {/* Future menu items can be added here */}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>More Options</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </>
            ) : (
              <>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        className="inline-flex items-center justify-center shrink-0 w-9 h-9 p-0 bg-teal-600"
                        onClick={handleCreateClick}
                      >
                        <Plus size={18} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Create Document</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-9 w-9 shrink-0 border border-gray-200 bg-white"
                        onClick={toggleFilterSidePanel}
                      >
                        <Filter size={18} className="text-gray-700" />
                        {countActiveFilters() > 0 && (
                          <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            {countActiveFilters()}
                          </span>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Filters</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-9 w-9 shrink-0 border border-gray-200 bg-white"
                          >
                            <MoreVertical size={18} className="text-gray-600" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="z-50 bg-white"
                        >
                          <DropdownMenuItem
                            onClick={resetAllFilters}
                            className="flex items-center gap-2"
                          >
                            <RotateCcw size={16} className="text-gray-600" />
                            Reset Filters
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>More Options</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Filter Side Panel Component
interface FilterSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  appliedFilters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  statusOptions: FilterOption[];
  categoriesOptions: FilterOption[];
  departmentsOptions: FilterOption[];
  assigneeOptions: FilterOption[];
  processesOptions: FilterOption[];
}

const FilterSidePanel: React.FC<FilterSidePanelProps> = ({
  isOpen,
  onClose,
  appliedFilters,
  onApplyFilters,
  statusOptions,
  categoriesOptions,
  departmentsOptions,
  assigneeOptions,
  processesOptions,
}) => {
  const [localFilters, setLocalFilters] = useState<FilterState>(appliedFilters);

  useEffect(() => {
    setLocalFilters(appliedFilters);
  }, [appliedFilters]);

  const handleFilterChange = (
    filterType: keyof FilterState,
    values: string[]
  ) => {
    setLocalFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters: FilterState = {
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: [],
    };
    setLocalFilters(resetFilters);
    onApplyFilters(resetFilters);
  };

  const renderFilterSection = (
    title: string,
    filterKey: keyof FilterState,
    options: FilterOption[]
  ) => {
    const getOptionLabel = (optionId: string) => {
      const option = options.find((opt) => opt.id === optionId);
      return option ? option.label : optionId;
    };

    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium mb-2 flex items-center justify-between">
          <span>{title}</span>
          {localFilters[filterKey].length > 0 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {localFilters[filterKey].length}
            </span>
          )}
        </h4>

        {/* Filter Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="flex w-full items-center justify-between border rounded-md py-2 px-3 text-sm cursor-pointer bg-white hover:bg-gray-50">
              <span
                className={
                  localFilters[filterKey].length > 0
                    ? "text-gray-900"
                    : "text-gray-500"
                }
              >
                {localFilters[filterKey].length === 0
                  ? `Select ${title.toLowerCase()}...`
                  : localFilters[filterKey].length === 1
                  ? getOptionLabel(localFilters[filterKey][0])
                  : `${localFilters[filterKey].length} selected`}
              </span>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[220px] p-0 bg-white z-50"
            align="start"
          >
            <div className="flex flex-col p-2 max-h-[280px] overflow-y-auto">
              {options.map((option) => (
                <div
                  key={option.id}
                  className="flex items-center space-x-2 py-1"
                >
                  <input
                    type="checkbox"
                    id={`${filterKey}-${option.id}`}
                    checked={localFilters[filterKey].includes(option.id)}
                    onChange={(e) => {
                      const currentValues = localFilters[filterKey];
                      const newValues = e.target.checked
                        ? [...currentValues, option.id]
                        : currentValues.filter((v) => v !== option.id);
                      handleFilterChange(filterKey, newValues);
                    }}
                    className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                  />
                  <label
                    htmlFor={`${filterKey}-${option.id}`}
                    className="text-sm font-medium leading-none cursor-pointer flex-1"
                  >
                    {option.label} ({option.count})
                  </label>
                </div>
              ))}
            </div>
            <div className="flex justify-end p-2 border-t">
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => {
                  handleFilterChange(filterKey, []);
                }}
              >
                Clear
              </Button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Selected Filter Badges */}
        {localFilters[filterKey].length > 0 && (
          <div className="flex flex-wrap gap-1">
            {localFilters[filterKey].map((optionId) => (
              <span
                key={optionId}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {getOptionLabel(optionId)}
                <button
                  onClick={() => {
                    const newValues = localFilters[filterKey].filter(
                      (id) => id !== optionId
                    );
                    handleFilterChange(filterKey, newValues);
                  }}
                  className="ml-1 text-gray-500 hover:text-gray-700"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      <div className="absolute right-0 top-0 h-full w-80 bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>

          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto px-6 py-4 space-y-6">
            {renderFilterSection("Status", "status", statusOptions)}
            {renderFilterSection("Categories", "categories", categoriesOptions)}
            {renderFilterSection(
              "Departments",
              "departments",
              departmentsOptions
            )}
            {renderFilterSection("Assignee", "assignee", assigneeOptions)}
            {renderFilterSection("Processes", "processes", processesOptions)}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 px-6 py-4 space-y-3">
            <Button
              onClick={handleApply}
              className="w-full bg-teal-600 hover:bg-teal-700 text-white"
            >
              Apply Filters
            </Button>
            <Button onClick={handleReset} variant="outline" className="w-full">
              Reset All
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// TABLE COMPONENTS
// ============================================================================

// Pagination Component
interface VendorTablePaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  pageSizeOptions?: number[];
  total: number;
  className?: string;
}

const VendorTablePagination: React.FC<VendorTablePaginationProps> = ({
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 25, 50],
  total,
  className = "",
}) => {
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(startItem + pageSize - 1, total);

  return (
    <div className={`flex justify-between items-center p-2 ${className}`}>
      <div className="hidden md:block text-sm text-gray-500">
        {totalPages > 0 ? (
          <>
            {startItem} to {endItem} of {total}
          </>
        ) : (
          "0 items"
        )}
      </div>

      <div className="flex items-center space-x-2">
        <div className="flex items-center mr-2">
          <span className="text-sm text-gray-500 mr-2">Page Size:</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value: string) => onPageSizeChange?.(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize.toString()} />
            </SelectTrigger>
            <SelectContent>
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <span className="text-sm text-gray-500 px-2">
            {currentPage} of {totalPages || 1}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage >= totalPages}
            className="h-8 w-8 p-0"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Resizable Table Component
interface ResizableTableProps {
  children: React.ReactNode;
  className?: string;
  minColumnWidth?: number;
  onColumnResize?: (columnIndex: number, newWidth: number) => void;
}

const ResizableTable: React.FC<ResizableTableProps> = ({
  children,
  className,
  minColumnWidth = 80,
  onColumnResize,
}) => {
  const tableRef = useRef<HTMLTableElement>(null);
  const [isResizing, setIsResizing] = useState(false);

  const initializeResizeHandles = useCallback(() => {
    const table = tableRef.current;
    if (!table) return;

    const headers = table.querySelectorAll("th");

    table
      .querySelectorAll(".resize-handle")
      .forEach((handle) => handle.remove());

    headers.forEach((th, index) => {
      if (index === headers.length - 1) return;

      th.style.position = "relative";
      th.style.minWidth = th.style.width || `${minColumnWidth}px`;

      const resizeHandle = document.createElement("div");
      resizeHandle.className = "resize-handle";
      resizeHandle.style.cssText = `
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        cursor: col-resize;
        background: transparent;
        border-right: 2px solid transparent;
        transition: all 0.2s ease;
        z-index: 10;
        user-select: none;
      `;

      const handleMouseEnter = () => {
        if (!isResizing) {
          resizeHandle.style.borderRightColor = "#3b82f6";
          resizeHandle.style.background = "rgba(59, 130, 246, 0.1)";
        }
      };

      const handleMouseLeave = () => {
        if (!isResizing) {
          resizeHandle.style.borderRightColor = "transparent";
          resizeHandle.style.background = "transparent";
        }
      };

      resizeHandle.addEventListener("mouseenter", handleMouseEnter);
      resizeHandle.addEventListener("mouseleave", handleMouseLeave);

      const handleMouseDown = (e: MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        setIsResizing(true);

        const startX = e.clientX;
        const startWidth = th.offsetWidth;

        resizeHandle.style.borderRightColor = "#3b82f6";
        resizeHandle.style.background = "rgba(59, 130, 246, 0.2)";
        document.body.style.cursor = "col-resize";
        document.body.style.userSelect = "none";

        const handleMouseMove = (e: MouseEvent) => {
          const diff = e.clientX - startX;
          const newWidth = Math.max(minColumnWidth, startWidth + diff);

          th.style.width = `${newWidth}px`;
          th.style.minWidth = `${newWidth}px`;
          th.style.maxWidth = `${newWidth}px`;

          if (onColumnResize) {
            onColumnResize(index, newWidth);
          }
        };

        const handleMouseUp = () => {
          setIsResizing(false);

          resizeHandle.style.borderRightColor = "transparent";
          resizeHandle.style.background = "transparent";
          document.body.style.cursor = "";
          document.body.style.userSelect = "";

          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      };

      resizeHandle.addEventListener("mousedown", handleMouseDown);
      th.appendChild(resizeHandle);
    });
  }, [isResizing, minColumnWidth, onColumnResize]);

  useEffect(() => {
    const timeoutId = setTimeout(initializeResizeHandles, 0);

    return () => {
      clearTimeout(timeoutId);
      const table = tableRef.current;
      if (table) {
        table
          .querySelectorAll(".resize-handle")
          .forEach((handle) => handle.remove());
      }
    };
  }, [children, initializeResizeHandles]);

  return (
    <div className="relative overflow-auto">
      <Table
        ref={tableRef}
        className={`${className} ${isResizing ? "select-none" : ""}`}
      >
        {children}
      </Table>
    </div>
  );
};

// Document Table Header Component
interface DocumentTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  isMobile: boolean;
  showExpandColumn: boolean;
}

const DocumentTableHeader: React.FC<DocumentTableHeaderProps> = ({
  sortField,
  sortDirection,
  onSort,
  isMobile,
  showExpandColumn,
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
      ) : (
        <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />
      );
    }
    return (
      <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />
    );
  };

  if (isMobile) {
    return (
      <TableHeader>
        <TableRow>
          <TableHead className="w-10 p-2 bg-gray-50">
            <span className="sr-only">Expand</span>
          </TableHead>
          <TableHead
            className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 p-2 bg-gray-50 group"
            onClick={() => onSort("title")}
          >
            <div className="flex items-center">
              Title
              {renderSortIcon("title")}
            </div>
          </TableHead>
          <TableHead
            className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 p-2 bg-gray-50 group"
            onClick={() => onSort("status")}
          >
            <div className="flex items-center">
              Status
              {renderSortIcon("status")}
            </div>
          </TableHead>
          <TableHead className="w-10 p-2 bg-gray-50">
            <span className="sr-only">Actions</span>
          </TableHead>
        </TableRow>
      </TableHeader>
    );
  }

  return (
    <TableHeader>
      <TableRow className="bg-gray-50">
        {showExpandColumn && (
          <TableHead className="w-12 p-3 bg-gray-50">
            <span className="sr-only">Expand</span>
          </TableHead>
        )}
        <TableHead
          className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 px-4 bg-gray-50 group w-1/4 min-w-[200px]"
          onClick={() => onSort("title")}
        >
          <div className="flex items-center justify-between">
            <span>TITLE</span>
            {renderSortIcon("title")}
          </div>
        </TableHead>
        <TableHead
          className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 px-4 bg-gray-50 group w-24"
          onClick={() => onSort("status")}
        >
          <div className="flex items-center justify-between">
            <span>STATUS</span>
            {renderSortIcon("status")}
          </div>
        </TableHead>

        {!showExpandColumn && (
          <>
            <TableHead
              className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 px-4 bg-gray-50 group w-28"
              onClick={() => onSort("department")}
            >
              <div className="flex items-center justify-between">
                <span>DEPARTMENT</span>
                {renderSortIcon("department")}
              </div>
            </TableHead>
            <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
              CATEGORY
            </TableHead>
            <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-28">
              PROCESSES
            </TableHead>
            <TableHead
              className="text-xs font-semibold text-gray-700 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 px-4 bg-gray-50 group w-24"
              onClick={() => onSort("assignee")}
            >
              <div className="flex items-center justify-between">
                <span>ASSIGNEE</span>
                {renderSortIcon("assignee")}
              </div>
            </TableHead>
            <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
              PUBLISH DATE
            </TableHead>
            <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
              REVIEW DATE
            </TableHead>
            <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-20">
              APPROVER
            </TableHead>
          </>
        )}

        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 text-right w-20">
          ACTIONS
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

// Document Status Badge Component
interface DocumentStatusBadgeProps {
  status: string;
}

const DocumentStatusBadge: React.FC<DocumentStatusBadgeProps> = ({
  status,
}) => {
  const lowercase = status.toLowerCase();

  if (lowercase === "draft") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
        Draft
      </span>
    );
  }

  if (
    lowercase === "review" ||
    lowercase === "in review" ||
    lowercase === "needs-approval"
  ) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 text-amber-700 border border-amber-200">
        Needs Approval
      </span>
    );
  }

  if (lowercase === "approved") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
        Approved
      </span>
    );
  }

  if (lowercase === "published") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-50 text-teal-700 border border-teal-200">
        Published
      </span>
    );
  }

  if (lowercase === "expired") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
        Expired
      </span>
    );
  }

  if (lowercase === "rejected") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200">
        Rejected
      </span>
    );
  }

  if (lowercase === "archived") {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-300">
        Archived
      </span>
    );
  }

  // Default
  return (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-300">
      {status}
    </span>
  );
};

// Document Title Component
interface DocumentTitleProps {
  document: Document;
}

const DocumentTitle: React.FC<DocumentTitleProps> = ({ document }) => {
  const handleClick = () => {
    console.log("Navigating to document details:", document.id);
    console.log("Document data:", document);

    // Simulate navigation - in a real app this would use useNavigate
    // navigate(`/document/${document.id}`);
    alert(`Would navigate to document: ${document.title} (ID: ${document.id})`);
  };

  return (
    <div className="flex flex-col min-w-0">
      <div className="flex items-center min-w-0">
        <span
          className="text-blue-600 cursor-pointer hover:text-blue-700 font-bold truncate"
          onClick={handleClick}
          title={document.title}
        >
          {document.title}
          {document.version && (
            <span className="ml-2 bg-gray-100 text-gray-700 rounded-full px-3 text-xs py-0.5 flex-shrink-0">
              {document.version}
            </span>
          )}
        </span>
      </div>
      <div className="flex items-center mt-1 min-w-0">
        <span className="text-xs text-gray-500 mr-2 truncate">
          ID: {document.id}
        </span>
      </div>
    </div>
  );
};

// Review Date Cell Component
interface ReviewDateCellProps {
  reviewDate?: string;
}

const ReviewDateCell: React.FC<ReviewDateCellProps> = ({ reviewDate }) => {
  if (!reviewDate) return <span className="text-gray-500">-</span>;

  const isOverdue = new Date(reviewDate) < new Date();

  return (
    <span className={isOverdue ? "text-red-600 font-medium" : "text-gray-700"}>
      {reviewDate}
    </span>
  );
};

// Mobile Expand Button Component
interface MobileExpandButtonProps {
  isExpanded: boolean;
  onClick: () => void;
}

const MobileExpandButton: React.FC<MobileExpandButtonProps> = ({
  isExpanded,
  onClick,
}) => (
  <button onClick={onClick} className="focus:outline-none">
    {isExpanded ? (
      <ChevronDown size={16} className="text-gray-400" />
    ) : (
      <ChevronRight size={16} className="text-gray-400" />
    )}
  </button>
);

// Mobile Document Title Component
interface MobileDocumentTitleProps {
  document: Document;
}

const MobileDocumentTitle: React.FC<MobileDocumentTitleProps> = ({
  document,
}) => {
  const handleClick = () => {
    console.log("Mobile - Navigating to document details:", document.id);
    console.log("Document data:", document);

    // Simulate navigation - in a real app this would use useNavigate
    // navigate(`/document/${document.id}`);
    alert(`Would navigate to document: ${document.title} (ID: ${document.id})`);
  };

  return (
    <div className="flex flex-col min-w-0">
      <div className="flex items-center gap-2 min-w-0">
        <span
          className="font-bold text-blue-600 cursor-pointer hover:text-blue-700 text-sm truncate"
          onClick={handleClick}
          title={document.title}
        >
          {document.title}
          {document.version && (
            <span className="ml-2 text-xs bg-gray-100 text-gray-700 rounded-full px-3 py-0.5 flex-shrink-0">
              {document.version}
            </span>
          )}
        </span>
      </div>
      <div className="flex items-center mt-1 min-w-0">
        <span className="text-xs text-gray-500 mr-2 truncate">
          ID: {document.id}
        </span>
      </div>
    </div>
  );
};

// Mobile Expanded Details Component
interface MobileExpandedDetailsProps {
  document: Document;
  index: number;
}

const MobileExpandedDetails: React.FC<MobileExpandedDetailsProps> = ({
  document,
  index,
}) => (
  <TableRow
    className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}
  >
    <TableCell colSpan={4} className="px-4 py-3">
      <div className="grid grid-cols-2 gap-3 text-sm">
        <div className="min-w-0">
          <span className="font-medium text-gray-500">Category:</span>
          <span className="ml-2 text-gray-700 truncate block">
            {document.category}
          </span>
        </div>
        <div className="min-w-0">
          <span className="font-medium text-gray-500">Processes:</span>
          <span className="ml-2 text-gray-700 truncate block">
            {document.processes || "-"}
          </span>
        </div>
        <div className="min-w-0">
          <span className="font-medium text-gray-500">Assignee:</span>
          <span className="ml-2 text-gray-700 truncate block">
            {document.assignee}
          </span>
        </div>
        <div className="min-w-0">
          <span className="font-medium text-gray-500">Approver:</span>
          <span className="ml-2 text-gray-700 truncate block">
            {document.approver}
          </span>
        </div>
        <div className="min-w-0">
          <span className="font-medium text-gray-500">Published:</span>
          <span className="ml-2 text-gray-700 truncate block">
            {document.publishedDate || "-"}
          </span>
        </div>
        <div>
          <span className="font-medium text-gray-500">Review Date:</span>
          <span className="ml-2">
            <ReviewDateCell reviewDate={document.reviewDate} />
          </span>
        </div>
      </div>
    </TableCell>
  </TableRow>
);

// Document Table Row Mobile Component
interface DocumentTableRowMobileProps {
  document: Document;
  index: number;
}

const DocumentTableRowMobile: React.FC<DocumentTableRowMobileProps> = ({
  document,
  index,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      <TableRow
        key={document.id}
        className={`${
          index % 2 === 0 ? "bg-white" : "bg-gray-50"
        } hover:bg-gray-100 transition-colors`}
      >
        <TableCell className="pl-4 pr-2 py-4 w-10">
          <MobileExpandButton isExpanded={isExpanded} onClick={toggleExpand} />
        </TableCell>

        <TableCell className="py-4 max-w-0">
          <div className="truncate">
            <MobileDocumentTitle document={document} />
          </div>
        </TableCell>

        <TableCell className="py-4 pr-4">
          <DocumentStatusBadge status={document.status} />
        </TableCell>

        <TableCell className="py-4 px-2 w-10">
          <DocumentActions document={document} />
        </TableCell>
      </TableRow>

      {isExpanded && (
        <>
          <TableRow
            className={`${
              index % 2 === 0 ? "bg-white" : "bg-gray-50"
            } border-t-0`}
          >
            <TableCell colSpan={4} className="px-2 py-1">
              <div className="flex items-center justify-end gap-2">
                <span className="text-sm text-gray-500">
                  Additional actions available in expanded view
                </span>
              </div>
            </TableCell>
          </TableRow>

          <MobileExpandedDetails document={document} index={index} />
        </>
      )}
    </>
  );
};

// Document Table Row Desktop Component
interface DocumentTableRowDesktopProps {
  document: Document;
  index: number;
  showExpandColumn: boolean;
}

const DocumentTableRowDesktop: React.FC<DocumentTableRowDesktopProps> = ({
  document: doc,
  index,
  showExpandColumn,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      <TableRow
        key={doc.id}
        className={`${
          index % 2 === 0 ? "bg-white" : "bg-gray-50"
        } hover:bg-gray-100 transition-colors`}
      >
        {showExpandColumn && (
          <TableCell className="w-12 p-3 text-center">
            <button onClick={handleExpandClick} className="focus:outline-none">
              {isExpanded ? (
                <ChevronDown size={16} className="text-gray-400" />
              ) : (
                <ChevronRight size={16} className="text-gray-400" />
              )}
            </button>
          </TableCell>
        )}

        <TableCell className="py-4 px-4 w-1/4 min-w-[200px] max-w-0">
          <div className="truncate">
            <DocumentTitle document={doc} />
          </div>
        </TableCell>

        <TableCell className="py-4 px-4 w-24">
          <DocumentStatusBadge status={doc.status} />
        </TableCell>

        {!showExpandColumn && (
          <>
            <TableCell className="py-4 px-4 w-28 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.department}
              </span>
            </TableCell>

            <TableCell className="py-4 px-4 w-24 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.category}
              </span>
            </TableCell>

            <TableCell className="py-4 px-4 w-28 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.processes || "-"}
              </span>
            </TableCell>

            <TableCell className="py-4 px-4 w-24 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.assignee}
              </span>
            </TableCell>

            <TableCell className="py-4 px-4 w-24 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.publishedDate}
              </span>
            </TableCell>

            <TableCell className="py-4 px-4 w-24 max-w-0">
              <div className="truncate">
                <ReviewDateCell reviewDate={doc.reviewDate} />
              </div>
            </TableCell>

            <TableCell className="py-4 px-4 w-20 max-w-0">
              <span className="text-gray-700 block truncate">
                {doc.approver}
              </span>
            </TableCell>
          </>
        )}

        <TableCell className="py-4 px-4 w-20 text-right">
          <DocumentActions document={doc} />
        </TableCell>
      </TableRow>

      {showExpandColumn && isExpanded && (
        <MobileExpandedDetails document={doc} index={index} />
      )}
    </>
  );
};

// Document Table Row Component
interface DocumentTableRowProps {
  document: Document;
  index: number;
  isMobile: boolean;
  showExpandColumn: boolean;
}

const DocumentTableRow: React.FC<DocumentTableRowProps> = ({
  document,
  index,
  isMobile,
  showExpandColumn,
}) => {
  if (isMobile) {
    return <DocumentTableRowMobile document={document} index={index} />;
  }

  return (
    <DocumentTableRowDesktop
      document={document}
      index={index}
      showExpandColumn={showExpandColumn}
    />
  );
};

// Document Table Props
interface DocumentTableProps {
  documents: Document[];
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  onSearch?: (term: string) => void;
}

// Document Table Core Component
const DocumentTableCore: React.FC<DocumentTableProps> = ({
  documents,
  currentPage,
  totalCount,
  pageSize,
  onPageChange,
  onPageSizeChange,
}) => {
  const isMobile = useIsMobile();
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [showExpandColumn, setShowExpandColumn] = useState(false);

  useEffect(() => {
    const checkIfExpandColumnNeeded = () => {
      if (isMobile) {
        setShowExpandColumn(false);
        return;
      }

      const containerWidth = window.innerWidth;
      const minRequiredWidth = 1200;
      setShowExpandColumn(containerWidth < minRequiredWidth);
    };

    checkIfExpandColumnNeeded();
    window.addEventListener("resize", checkIfExpandColumnNeeded);

    return () =>
      window.removeEventListener("resize", checkIfExpandColumnNeeded);
  }, [isMobile]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === "asc") {
        setSortDirection("desc");
      } else if (sortDirection === "desc") {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection("asc");
      }
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const sortedDocuments = useMemo(() => {
    if (!sortField || !sortDirection) return documents;

    return [...documents].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;

      const comparison = aValue.toString().localeCompare(bValue.toString());
      return sortDirection === "asc" ? comparison : -comparison;
    });
  }, [documents, sortField, sortDirection]);

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="w-full overflow-hidden">
        <ResizableTable className="w-full table-fixed">
          <DocumentTableHeader
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
            isMobile={isMobile}
            showExpandColumn={showExpandColumn}
          />
          <TableBody>
            {sortedDocuments.length > 0 ? (
              sortedDocuments.map((document, index) => (
                <DocumentTableRow
                  key={document.id}
                  document={document}
                  index={index}
                  isMobile={isMobile}
                  showExpandColumn={showExpandColumn}
                />
              ))
            ) : (
              <tr>
                <td
                  colSpan={isMobile ? 4 : 11}
                  className="text-center py-8 text-gray-500"
                >
                  No documents found
                </td>
              </tr>
            )}
          </TableBody>
        </ResizableTable>
      </div>

      {totalCount > 0 && (
        <VendorTablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          total={totalCount}
          className="border-t border-gray-200"
        />
      )}
    </div>
  );
};

// Document Table Component
const DocumentTable: React.FC<DocumentTableProps> = (props) => {
  return <DocumentTableCore {...props} />;
};

// Document Table Container Props
interface DocumentTableContainerProps {
  appliedFilters: FilterState;
  searchTerm: string;
  documents?: Document[];
}

// Document Table Container Component
const DocumentTableContainer: React.FC<DocumentTableContainerProps> = ({
  appliedFilters,
  searchTerm,
  documents = mockDocuments,
}) => {
  const { filteredDocuments } = useDocumentFilter({
    documents: documents,
    appliedFilters,
    initialSearchTerm: searchTerm,
  });

  const {
    currentPage,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData,
  } = useDocumentPagination({
    totalCount: filteredDocuments.length,
    initialPageSize: 10,
    initialPage: 1,
  });

  const paginatedDocuments = getPaginatedData(filteredDocuments);

  return (
    <div className="h-full flex flex-col w-full">
      <DocumentTable
        documents={paginatedDocuments}
        currentPage={currentPage}
        totalCount={filteredDocuments.length}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

// ============================================================================
// MAIN EXAMPLE COMPONENT
// ============================================================================

/**
 * DocumentTableExample - A complete, self-contained document table component
 *
 * This component includes:
 * - Search functionality
 * - Advanced filtering
 * - Sorting capabilities
 * - Pagination
 * - Mobile responsive design
 * - Resizable columns
 *
 * Usage:
 * ```tsx
 * import { DocumentTableExample } from './DocumentTableExample';
 *
 * function App() {
 *   return (
 *     <div className="p-4">
 *       <DocumentTableExample />
 *     </div>
 *   );
 * }
 * ```
 *
 * You can also pass custom documents:
 * ```tsx
 * <DocumentTableExample documents={myCustomDocuments} />
 * ```
 */

interface DocumentTableExampleProps {
  documents?: Document[];
  className?: string;
}

export const DocumentTableExample: React.FC<DocumentTableExampleProps> = ({
  documents = mockDocuments,
  className,
}) => {
  // --- DYNAMIC FILTER OPTIONS ---
  const normalizedDocs = React.useMemo(() => normalizeDocuments(documents), [documents]);
  const statusOptions = React.useMemo(
    () => generateFilterOptions(normalizedDocs, "status"),
    [normalizedDocs]
  );
  const categoriesOptions = React.useMemo(
    () => generateFilterOptions(normalizedDocs, "category"),
    [normalizedDocs]
  );
  const departmentsOptions = React.useMemo(
    () => generateFilterOptions(normalizedDocs, "department"),
    [normalizedDocs]
  );
  const assigneeOptions = React.useMemo(
    () => generateFilterOptions(normalizedDocs, "assignee"),
    [normalizedDocs]
  );
  const processesOptions = React.useMemo(
    () => generateFilterOptions(normalizedDocs, "processes"),
    [normalizedDocs]
  );

  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: [],
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterSidePanelOpen, setIsFilterSidePanelOpen] = useState(false);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleApplyFilters = (filters: FilterState) => {
    setAppliedFilters(filters);
  };

  const toggleFilterSidePanel = () => {
    setIsFilterSidePanelOpen(!isFilterSidePanelOpen);
  };

  return (
    <div className={cn("h-full w-full space-y-6", className)}>
      {/* Search and Filter Section */}
      <SearchAndFilter
        isFilterSidePanelOpen={isFilterSidePanelOpen}
        toggleFilterSidePanel={toggleFilterSidePanel}
        appliedFilters={appliedFilters}
        onApplyFilters={handleApplyFilters}
        onSearch={handleSearch}
        searchTerm={searchTerm}
        statusOptions={statusOptions}
        categoriesOptions={categoriesOptions}
        departmentsOptions={departmentsOptions}
        assigneeOptions={assigneeOptions}
        processesOptions={processesOptions}
      />

      {/* Document Table Section */}
      <div className="flex-1 min-h-0">
        <DocumentTableContainer
          appliedFilters={appliedFilters}
          searchTerm={searchTerm}
          documents={normalizedDocs}
        />
      </div>

      {/* Filter Side Panel */}
      <FilterSidePanel
        isOpen={isFilterSidePanelOpen}
        onClose={toggleFilterSidePanel}
        appliedFilters={appliedFilters}
        onApplyFilters={handleApplyFilters}
        statusOptions={statusOptions}
        categoriesOptions={categoriesOptions}
        departmentsOptions={departmentsOptions}
        assigneeOptions={assigneeOptions}
        processesOptions={processesOptions}
      />
    </div>
  );
};

// Export all the main components for individual use
export {
  // Main components
  DocumentTableContainer,
  SearchAndFilter,
  DocumentTable,

  // Table components
  DocumentTableCore,
  DocumentTableHeader,
  DocumentTableRow,
  DocumentTableRowMobile,
  DocumentTableRowDesktop,
  DocumentActions,
  DocumentTitle,
  DocumentStatusBadge,
  ReviewDateCell,
  MobileExpandButton,
  MobileDocumentTitle,
  MobileExpandedDetails,

  // UI components
  Button,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectItem,
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,

  // Utility components
  SearchBar,
  StatusFilter,
  FilterSidePanel,
  VendorTablePagination,
  ResizableTable,

  // Hooks
  useDocumentFilter,
  useDocumentPagination,
  useIsMobile,

  // Utilities
  cn,

  // Types
  type Document,
  type FilterState,
  type FilterOption,
  type SortField,
  type SortDirection,
  type DocumentTableProps,
  type DocumentTableContainerProps,

  // Mock data
  mockDocuments,
  statusOptions,
  categoriesOptions,
  departmentsOptions,
  assigneeOptions,
  processesOptions,
};

// Default export
export default DocumentTableExample;
