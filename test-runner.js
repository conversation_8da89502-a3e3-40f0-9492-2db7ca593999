#!/usr/bin/env node

/**
 * Test Runner for DocumentTableExample Component
 *
 * This script helps you test the DocumentTableExample component in different ways:
 * 1. Static analysis of the component file
 * 2. Validation of TypeScript types
 * 3. Component structure verification
 * 4. Mock data validation
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DocumentTableTester {
  constructor() {
    this.testResults = [];
    this.componentPath = "./DocumentTableExample.tsx";
    this.testPath = "./DocumentTableTest.tsx";
  }

  log(message, type = "info") {
    const timestamp = new Date().toISOString();
    const prefix =
      {
        info: "📋",
        success: "✅",
        error: "❌",
        warning: "⚠️",
      }[type] || "ℹ️";

    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  addTestResult(testName, status, message, details = "") {
    this.testResults.push({
      testName,
      status,
      message,
      details,
      timestamp: new Date().toISOString(),
    });
  }

  async runStaticAnalysis() {
    this.log("Starting static analysis of DocumentTableExample.tsx...", "info");

    try {
      // Check if component file exists
      if (!fs.existsSync(this.componentPath)) {
        this.addTestResult(
          "File Existence",
          "FAIL",
          "DocumentTableExample.tsx not found",
          `Expected file at: ${path.resolve(this.componentPath)}`
        );
        return;
      }

      this.addTestResult(
        "File Existence",
        "PASS",
        "DocumentTableExample.tsx found",
        `File size: ${fs.statSync(this.componentPath).size} bytes`
      );

      // Read and analyze component content
      const content = fs.readFileSync(this.componentPath, "utf8");

      // Test 1: Check for required imports
      const requiredImports = ["React", "useState", "useEffect", "useMemo"];
      const missingImports = requiredImports.filter(
        (imp) => !content.includes(imp)
      );

      if (missingImports.length === 0) {
        this.addTestResult(
          "Required Imports",
          "PASS",
          "All required React imports found",
          `Checked: ${requiredImports.join(", ")}`
        );
      } else {
        this.addTestResult(
          "Required Imports",
          "FAIL",
          `Missing imports: ${missingImports.join(", ")}`,
          "Component may not function correctly without these imports"
        );
      }

      // Test 2: Check for main component export
      if (
        content.includes("export const DocumentTableExample") ||
        content.includes("export default DocumentTableExample")
      ) {
        this.addTestResult(
          "Main Component Export",
          "PASS",
          "DocumentTableExample component is properly exported",
          "Component can be imported and used in other files"
        );
      } else {
        this.addTestResult(
          "Main Component Export",
          "FAIL",
          "DocumentTableExample component export not found",
          "Component may not be importable"
        );
      }

      // Test 3: Check for TypeScript interfaces
      const requiredInterfaces = ["Document", "FilterState", "FilterOption"];
      const foundInterfaces = requiredInterfaces.filter(
        (iface) =>
          content.includes(`interface ${iface}`) ||
          content.includes(`export interface ${iface}`)
      );

      if (foundInterfaces.length === requiredInterfaces.length) {
        this.addTestResult(
          "TypeScript Interfaces",
          "PASS",
          "All required TypeScript interfaces found",
          `Found: ${foundInterfaces.join(", ")}`
        );
      } else {
        const missing = requiredInterfaces.filter(
          (iface) => !foundInterfaces.includes(iface)
        );
        this.addTestResult(
          "TypeScript Interfaces",
          "FAIL",
          `Missing interfaces: ${missing.join(", ")}`,
          "Type safety may be compromised"
        );
      }

      // Test 4: Check for custom hooks
      const requiredHooks = [
        "useDocumentFilter",
        "useDocumentPagination",
        "useIsMobile",
      ];
      const foundHooks = requiredHooks.filter((hook) => content.includes(hook));

      if (foundHooks.length === requiredHooks.length) {
        this.addTestResult(
          "Custom Hooks",
          "PASS",
          "All required custom hooks found",
          `Found: ${foundHooks.join(", ")}`
        );
      } else {
        const missing = requiredHooks.filter(
          (hook) => !foundHooks.includes(hook)
        );
        this.addTestResult(
          "Custom Hooks",
          "FAIL",
          `Missing hooks: ${missing.join(", ")}`,
          "Component functionality may be incomplete"
        );
      }

      // Test 5: Check for mock data
      if (
        content.includes("mockDocuments") &&
        content.includes("statusOptions")
      ) {
        this.addTestResult(
          "Mock Data",
          "PASS",
          "Mock data and options found",
          "Component includes sample data for testing"
        );
      } else {
        this.addTestResult(
          "Mock Data",
          "FAIL",
          "Mock data not found",
          "Component may not work without external data"
        );
      }

      // Test 6: Check for UI components
      const uiComponents = ["Button", "Table", "Select", "SearchBar"];
      const foundComponents = uiComponents.filter((comp) =>
        content.includes(`export const ${comp}`)
      );

      this.addTestResult(
        "UI Components",
        foundComponents.length > 0 ? "PASS" : "FAIL",
        `Found ${foundComponents.length}/${uiComponents.length} UI components`,
        `Found: ${foundComponents.join(", ")}`
      );

      // Test 7: Check file size and complexity
      const lines = content.split("\n").length;
      const functions = (
        content.match(/function\s+\w+|const\s+\w+\s*=\s*\(/g) || []
      ).length;

      this.addTestResult(
        "Code Complexity",
        "PASS",
        `Component has ${lines} lines and ${functions} functions`,
        lines > 2000
          ? "Large file - consider splitting into modules"
          : "Reasonable file size"
      );
    } catch (error) {
      this.addTestResult(
        "Static Analysis",
        "FAIL",
        "Error during static analysis",
        error.message
      );
    }
  }

  async validateMockData() {
    this.log("Validating mock data structure...", "info");

    try {
      const content = fs.readFileSync(this.componentPath, "utf8");

      // Extract mock documents (simplified regex-based extraction)
      const mockDataMatch = content.match(/export const mockDocuments[^;]+;/s);

      if (mockDataMatch) {
        this.addTestResult(
          "Mock Data Structure",
          "PASS",
          "Mock documents array found",
          "Sample data is available for testing"
        );

        // Check for required document fields in the mock data
        const requiredFields = [
          "id",
          "title",
          "department",
          "category",
          "status",
          "assignee",
          "approver",
        ];
        const mockDataString = mockDataMatch[0];
        const missingFields = requiredFields.filter(
          (field) => !mockDataString.includes(`"${field}"`)
        );

        if (missingFields.length === 0) {
          this.addTestResult(
            "Mock Data Fields",
            "PASS",
            "All required fields present in mock data",
            `Validated: ${requiredFields.join(", ")}`
          );
        } else {
          this.addTestResult(
            "Mock Data Fields",
            "WARNING",
            `Some fields may be missing: ${missingFields.join(", ")}`,
            "Mock data might not fully represent real data structure"
          );
        }
      } else {
        this.addTestResult(
          "Mock Data Structure",
          "FAIL",
          "Mock documents array not found",
          "Component may not work without external data source"
        );
      }
    } catch (error) {
      this.addTestResult(
        "Mock Data Validation",
        "FAIL",
        "Error validating mock data",
        error.message
      );
    }
  }

  async checkDependencies() {
    this.log("Checking dependencies...", "info");

    try {
      // Check if package.json exists
      if (fs.existsSync("./package.json")) {
        const packageJson = JSON.parse(
          fs.readFileSync("./package.json", "utf8")
        );
        const dependencies = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies,
        };

        // Check for required dependencies
        const requiredDeps = ["react", "lucide-react"];
        const missingDeps = requiredDeps.filter((dep) => !dependencies[dep]);

        if (missingDeps.length === 0) {
          this.addTestResult(
            "Dependencies",
            "PASS",
            "All required dependencies found",
            `React version: ${dependencies.react || "unknown"}`
          );
        } else {
          this.addTestResult(
            "Dependencies",
            "WARNING",
            `Missing dependencies: ${missingDeps.join(", ")}`,
            "Run: npm install lucide-react"
          );
        }
      } else {
        this.addTestResult(
          "Dependencies",
          "WARNING",
          "package.json not found",
          "Cannot verify dependencies"
        );
      }
    } catch (error) {
      this.addTestResult(
        "Dependency Check",
        "FAIL",
        "Error checking dependencies",
        error.message
      );
    }
  }

  generateReport() {
    this.log("Generating test report...", "info");

    const passCount = this.testResults.filter(
      (r) => r.status === "PASS"
    ).length;
    const failCount = this.testResults.filter(
      (r) => r.status === "FAIL"
    ).length;
    const warningCount = this.testResults.filter(
      (r) => r.status === "WARNING"
    ).length;

    console.log("\n" + "=".repeat(80));
    console.log("📊 DOCUMENTTABLEEXAMPLE TEST REPORT");
    console.log("=".repeat(80));
    console.log(`✅ Passed: ${passCount}`);
    console.log(`❌ Failed: ${failCount}`);
    console.log(`⚠️  Warnings: ${warningCount}`);
    console.log(`📋 Total Tests: ${this.testResults.length}`);
    console.log("=".repeat(80));

    this.testResults.forEach((result, index) => {
      const icon =
        {
          PASS: "✅",
          FAIL: "❌",
          WARNING: "⚠️",
        }[result.status] || "❓";

      console.log(`\n${index + 1}. ${icon} ${result.testName}`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Message: ${result.message}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    });

    console.log("\n" + "=".repeat(80));

    if (failCount === 0) {
      this.log(
        "🎉 All critical tests passed! Component is ready for use.",
        "success"
      );
    } else {
      this.log(
        `⚠️ ${failCount} test(s) failed. Please review and fix issues before using the component.`,
        "warning"
      );
    }

    console.log("\n📝 Next Steps:");
    console.log("1. Fix any failed tests");
    console.log("2. Install missing dependencies: npm install lucide-react");
    console.log("3. Add Tailwind CSS to your project for styling");
    console.log(
      '4. Import and use: import { DocumentTableExample } from "./DocumentTableExample"'
    );
    console.log("5. Use the DocumentTableTest.tsx for interactive testing");
  }

  async runAllTests() {
    this.log("🚀 Starting DocumentTableExample test suite...", "info");

    await this.runStaticAnalysis();
    await this.validateMockData();
    await this.checkDependencies();

    this.generateReport();
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new DocumentTableTester();
  tester.runAllTests().catch((error) => {
    console.error("❌ Test runner failed:", error);
    process.exit(1);
  });
}

export default DocumentTableTester;
